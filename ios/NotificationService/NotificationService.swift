////
////  NotificationService.swift
////  NotificationService
////
////  Created by Apple on 07/06/25.
////
//
//import UserNotifications
//
//class NotificationService: UNNotificationServiceExtension {
//
//    var contentHandler: ((UNNotificationContent) -> Void)?
//    var bestAttemptContent: UNMutableNotificationContent?
//
//    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
//        self.contentHandler = contentHandler
//        bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)
//        
//        if let bestAttemptContent = bestAttemptContent {
//            // Modify the notification content here...
//            bestAttemptContent.title = "\(bestAttemptContent.title) [modified]"
//            
//            contentHandler(bestAttemptContent)
//        }
//    }
//    
//    override func serviceExtensionTimeWillExpire() {
//        // Called just before the extension will be terminated by the system.
//        // Use this as an opportunity to deliver your "best attempt" at modified content, otherwise the original push payload will be used.
//        if let contentHandler = contentHandler, let bestAttemptContent =  bestAttemptContent {
//            contentHandler(bestAttemptContent)
//        }
//    }
//
//}
//
//import UserNotifications
//
//class NotificationService: UNNotificationServiceExtension {
//
//    var contentHandler: ((UNNotificationContent) -> Void)?
//    var bestAttemptContent: UNMutableNotificationContent?
//
//    // The App Group identifier you created in Step 2
//    let appGroupId = "group.com.gotcha4life.mentalfitnessgym"
//    // The key for storing the count in UserDefaults
//    let userDefaultsKey = "terminated_notification_count"
//
//    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
//        self.contentHandler = contentHandler
//        bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)
//
//        if let bestAttemptContent = bestAttemptContent {
//            // --- OUR COUNTING LOGIC START ---
//
//            // 1. Access the shared UserDefaults from our App Group
//            if let userDefaults = UserDefaults(suiteName: appGroupId) {
//                // 2. Get the current count, defaulting to 0 if it doesn't exist
//                var currentCount = userDefaults.integer(forKey: userDefaultsKey)
//
//                // 3. Increment the count
//                currentCount += 1
//
//                // 4. Save the new count back to shared UserDefaults
//                userDefaults.set(currentCount, forKey: userDefaultsKey)
//                
//                // Optional: You can modify the notification title to show the count
//                // bestAttemptContent.title = "\(bestAttemptContent.title) [\(currentCount) unread]"
//            }
//
//            // --- OUR COUNTING LOGIC END ---
//
//            // 5. Deliver the notification to the user
//            contentHandler(bestAttemptContent)
//        }
//    }
//
//    override func serviceExtensionTimeWillExpire() {
//        // Called just before the extension will be terminated by the system.
//        // Use this as a last chance to deliver the original content.
//        if let contentHandler = contentHandler, let bestAttemptContent = bestAttemptContent {
//            contentHandler(bestAttemptContent)
//        }
//    }
//}


import UserNotifications

class NotificationService: UNNotificationServiceExtension {

    var contentHandler: ((UNNotificationContent) -> Void)?
    var bestAttemptContent: UNMutableNotificationContent?

    // --- SHARED CONFIGURATION ---
    let appGroupId = "group.com.gotcha4life.mentalfitnessgym"
    let countKey = "terminated_notification_count"
    let typesListKey = "terminated_notification_types" // New key for our list

    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        self.contentHandler = contentHandler
        bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)

        if let bestAttemptContent = bestAttemptContent {

            // Access the shared UserDefaults from our App Group
            if let userDefaults = UserDefaults(suiteName: appGroupId) {
                
                // --- 1. LOGIC FOR INCREMENTING THE COUNT (Unchanged) ---
                var currentCount = userDefaults.integer(forKey: countKey)
                currentCount += 1
                userDefaults.set(currentCount, forKey: countKey)
                
                // --- 2. NEW LOGIC FOR 'type' LIST ---
                
                // Get the existing list of types, or an empty array if it doesn't exist
                var currentTypes = userDefaults.stringArray(forKey: typesListKey) ?? []
                
                // Extract the 'type' value from the notification's payload (userInfo)
                // We safely cast it to a String.
                if let notificationType = bestAttemptContent.userInfo["type"] as? String {
                    // Append the new type to our list
                    currentTypes.append(notificationType)
                    
                    // Save the updated list back to UserDefaults
                    userDefaults.set(currentTypes, forKey: typesListKey)
                }
                
                // Optional: You can still modify the notification content
                // bestAttemptContent.title = "\(bestAttemptContent.title) [\(currentCount)]"
            }

            // --- END OF OUR LOGIC ---

            // Deliver the notification to the user
            contentHandler(bestAttemptContent)
        }
    }

    override func serviceExtensionTimeWillExpire() {
        if let contentHandler = contentHandler, let bestAttemptContent = bestAttemptContent {
            contentHandler(bestAttemptContent)
        }
    }
}
