import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/storage/token_manager.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/core/utils/data_cache.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/splash/data/models/info_request.dart';
import 'package:gotcha_mfg_app/features/splash/presentation/blocs/splash/splash_cubit.dart';
import 'package:gotcha_mfg_app/locator.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_safe_area.dart';
import 'package:gotcha_mfg_app/shared/widgets/popup.dart';
import 'package:lottie/lottie.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/firebase_push.dart';
import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/device_info.dart';
import '../../../../core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/core/network/network_info.dart';

const version = 38;

/// The first page the user sees when they open the app.
@RoutePage()
class SplashPage extends StatefulWidget {
  /// Constructor
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  static const String _lastUpdatePopupKey = 'last_update_popup_time';

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView('Splash Page',
        properties: {'Code': 'screen_view.splash_page'});
    Future.delayed(const Duration(seconds: 4), () {
      _initialize();
    });
  }

  _initialize() async {
    final hasInternet = await sl<NetworkInfo>().isConnected;
    if (!hasInternet) {
      if (mounted) {
        CustomCupertinoAlertDialog.showAlertPopup(
          context,
          title: "No internet connection",
          content: "Please check your internet connection and try again.",
          onOk: () {
            _initialize();
            // mixpanel
            sl<MixpanelService>().trackButtonClick('Ok', properties: {
              'Page': 'Splash Page',
              'Code': 'click.splash_page.no_internet_retry'
            });
          },
        );
      }
      return;
    }
    var uniqueId = await sl<TokenManager>().getUniqueId();
    var accessToken = await sl<TokenManager>().getAccessToken();
    var fcmToken = await MFGPushNotification.messaging.getToken();
    info('>>>>fcmToken: $uniqueId');
    info('>>>>fcmToken: $fcmToken');
    info('========: $accessToken');
    String? deviceId = await getDeviceId();
    String? deviceName = await getDeviceName();
    info('========fcm---: $deviceId');

    String? deviceVersion = await getDeviceVersion();
    final String currentTimeZone = await FlutterTimezone.getLocalTimezone();
    if (uniqueId != null && accessToken != null) {
      _sendPendingPushStatistics();
      // User is already logged in - use startup identification
      sl<MixpanelService>().identifyOnStartup(uniqueId);
      var infoRequest = InfoRequest(
        deviceId: uniqueId,
        os: deviceName ?? "Device",
        osVersion: deviceVersion ?? "1",
        token: fcmToken,
        fcmId: deviceId,
        timezone: currentTimeZone,
        version: version.toString(),
      );
      if (mounted) context.read<SplashCubit>().info(infoRequest);
    } else {
      // New user - generate unique ID and identify
      var uniqueId = await sl<TokenManager>().generateUniqueId();
      sl<MixpanelService>().identifyOnStartup(uniqueId);
      var infoRequest = InfoRequest(
        deviceId: uniqueId,
        os: deviceName ?? "Device",
        osVersion: deviceVersion ?? "1",
        token: fcmToken,
        fcmId: deviceId,
        timezone: currentTimeZone,
        version: version.toString(),
      );
      if (mounted) context.read<SplashCubit>().info(infoRequest);
      // if (mounted) context.read<SplashCubit>().rerouteToOnboarding();
    }
  }

  // Send pending push statistics to Mixpanel
  void _sendPendingPushStatistics() async {
    if (!isIos) return;

    try {
      int count = await getTerminatedNotificationCount();
      List<String> types = await getTerminatedNotificationTypes();

      count = count.clamp(0, types.length);
      types = types.sublist(0, count);

      if (count > 0) {
        for (int i = 0; i < count; i++) {
          sl<MixpanelService>().trackPushNotificationDelivery(
            types[i],
            'Terminated',
          );
        }
        await resetNotificationCount();
      }
    } catch (e, stack) {
      info('Error sending pending push statistics: $e');
      info('Stacktrace: $stack');
    }
  }

  Future<bool> redirectToStore({
    required String androidStoreLink,
    required String iosStoreLink,
  }) async {
    String? targetLink;

    // --- Determine the correct link based on the platform ---
    if (Platform.isAndroid) {
      targetLink = androidStoreLink;
      info("Platform is Android, using link: $targetLink");
    } else if (Platform.isIOS) {
      targetLink = iosStoreLink;
      info("Platform is iOS, using link: $targetLink");
    } else {
      info(
          "Store redirection not supported on this platform: ${Platform.operatingSystem}");
      return false; // Indicate failure/unsupported platform
    }

    // --- Validate and parse the URL ---
    Uri? url;
    try {
      url = Uri.parse(targetLink);
    } on FormatException catch (e) {
      info("Invalid store URL format: $targetLink - Error: $e");
      return false;
    }

    // --- Attempt to launch the URL ---
    try {
      // LaunchMode.externalApplication is important to ensure it opens
      // the Store app directly, not an in-app browser.
      bool launched = await launchUrl(
        url,
        mode: LaunchMode.externalApplication,
      );

      if (!launched) {
        info("Could not launch store URL: $url");
        // Optionally, try launching without the mode as a fallback (less ideal)
        // launched = await launchUrl(url);
        // if(!launched) return false;
        return false;
      }
      info("Store URL launch attempted successfully.");
      return true; // Launch attempt was successful
    } catch (e) {
      info("Error launching store URL: $url - Error: $e");
      return false; // Indicate failure
    }
  }

  void _handleUpdatePressed(
      BuildContext context, String android, String ios) async {
    // You might want to show a loading indicator here
    bool success = await redirectToStore(
      androidStoreLink: android,
      iosStoreLink: ios,
    );
    // Hide loading indicator here

    if (!success) {
      // Optionally show an error message to the user if it failed
      if (context.mounted) {
        // Check if the widget is still in the tree
        SnackBarService.error(
            context: context, message: 'Could not open the app store.');
      }
    }
  }

  Future<int> checkVersion({
    required String latestAndroid,
    required String latestIos,
    required String minimumIos,
    required String minimumAndroid,
    required BuildContext context,
  }) async {
    // final PackageInfo packageInfo = await PackageInfo.fromPlatform();
    const int currentVersion = version;
    info(
        '-------current: $currentVersion----: $minimumAndroid----latest: $latestAndroid-- $minimumIos-- $latestIos');

    if (isIos) {
      final int minVersion = int.tryParse(minimumIos) ?? 0;
      final int latestVersion = int.tryParse(latestIos) ?? 0;

      if (currentVersion < minVersion) {
        return 1;
      } else if (currentVersion < latestVersion) {
        return 2;
      }
      return 3;
    } else {
      final int minVersion = int.tryParse(minimumAndroid) ?? 0;
      final int latestVersion = int.tryParse(latestAndroid) ?? 0;

      if (currentVersion < minVersion) {
        return 1;
      } else if (currentVersion < latestVersion) {
        return 2;
      }
      return 3;
    }
  }

  Future<bool> _shouldShowUpdatePopup() async {
    final prefs = await SharedPreferences.getInstance();
    final lastShown = prefs.getInt(_lastUpdatePopupKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    return now - lastShown >= const Duration(days: 3).inMilliseconds;
  }

  Future<void> _updateLastPopupTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(
      _lastUpdatePopupKey,
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  void _proceedToNextScreen(SplashInfoLoaded state, BuildContext context) {
    if (state.response.data?.isOnboardingComplete == true) {
      if (state.response.data?.isExerciseViewed == true ||
          state.response.data?.isReminderScheduled == true) {
        if (MFGPushNotification.hasPendingNotifications()) {
          MFGPushNotification.handlePendingNotification();
        } else {
          context.replaceRoute(HomeRoute(index: 0));
        }
      } else {
        context.replaceRoute(const WelcomeFeedRoute());
      }
    } else {
      context.replaceRoute(const OnboardingWelcomeRoute());
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SplashCubit, SplashState>(
      listener: (context, state) {
        if (state is SplashInfoLoaded) {
          DataCache().saveData(state.response);

          var versionCheck = checkVersion(
              context: context,
              minimumAndroid:
                  state.response.data?.miscellaneous?.minVersionAndroid ?? '0',
              minimumIos:
                  state.response.data?.miscellaneous?.minVersionIos ?? '0',
              latestAndroid:
                  state.response.data?.miscellaneous?.latestVersionAndroid ??
                      '0',
              latestIos:
                  state.response.data?.miscellaneous?.latestVersionIos ?? '0');
          versionCheck.then((version) async {
            if (version == 1) {
              CustomCupertinoAlertDialog.showAlertPopup(
                okButtonText: 'Update now',
                context,
                title: "Update required",
                content:
                    "Your app version is outdated. Please update to continue.",
                onOk: () {
                  _handleUpdatePressed(
                      context,
                      'https://play.google.com/store/apps/details?id=com.gotcha4life.mentalfitnessgym',
                      'https://apps.apple.com/us/app/the-mental-fitness-gym/id6742421861');
                  // mixpanel
                  sl<MixpanelService>().trackButtonClick('Update now',
                      properties: {
                        'Page': 'Splash Page',
                        'Code': 'click.splash_page.update_now'
                      });
                  return;
                },
              );
              return;
            } else if (version == 2) {
              final shouldShow = await _shouldShowUpdatePopup();
              if (shouldShow) {
                CustomCupertinoAlertDialog.yesOrNoPopup(
                  yesButtonText: 'Update now',
                  noButtonText: 'Later',
                  context,
                  title: "Update available",
                  content: "A new version of the app is available. Update now?",
                  onYes: () {
                    _handleUpdatePressed(
                        context,
                        'https://play.google.com/store/apps/details?id=com.gotcha4life.mentalfitnessgym',
                        'https://apps.apple.com/us/app/the-mental-fitness-gym/id6742421861');
                    // mixpanel
                    sl<MixpanelService>().trackButtonClick('Update now',
                        properties: {
                          'Page': 'Splash Page',
                          'Code': 'click.splash_page.update_now'
                        });
                    return;
                  },
                  onNo: () async {
                    await _updateLastPopupTime();
                    _proceedToNextScreen(state, context);
                  },
                );
              } else {
                _proceedToNextScreen(state, context);
              }
            } else {
              _proceedToNextScreen(state, context);
            }
          });
        }

        if (state is SplashInfoError) {
          context.replaceRoute(const OnboardingWelcomeRoute());
        }

        if (state is SplashReroute) {
          info("qqq1234 11");
          context.replaceRoute(const OnboardingWelcomeRoute());
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: AppBar(
            toolbarHeight: 0,
            elevation: 0,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.white,
              systemNavigationBarIconBrightness: Brightness.dark,
              statusBarBrightness: Brightness.light,
              statusBarIconBrightness: Brightness.dark,
              systemNavigationBarColor: Colors.white,
            ),
          ),
          body: AppSafeArea(
            child: Stack(
              children: [
                Positioned.fill(
                  bottom: isIos ? 20 : 8,
                  top: isIos ? 4 : 4,
                  right: 8,
                  left: 8,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(isIos ? 32 : 12),
                    clipBehavior: Clip.hardEdge,
                    child: Transform.scale(
                      scale: 1.2,
                      child: FittedBox(
                        fit: BoxFit.cover,
                        alignment: Alignment.center,
                        child: Lottie.asset(
                          AppAssets.splash,
                          fit: BoxFit.contain,
                          repeat: false,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
