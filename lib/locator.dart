import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:gotcha_mfg_app/config/router/app_router.dart';
import 'package:gotcha_mfg_app/core/network/network_client.dart';
import 'package:gotcha_mfg_app/core/network/network_config.dart';
import 'package:gotcha_mfg_app/core/network/network_info.dart';
import 'package:gotcha_mfg_app/core/storage/storage_service.dart';
import 'package:gotcha_mfg_app/core/storage/token_manager.dart';
import 'package:gotcha_mfg_app/features/auth/domain/usecases/forgot_password_usecase.dart';
import 'package:gotcha_mfg_app/features/auth/domain/usecases/sign_up_usecase.dart';
import 'package:gotcha_mfg_app/features/auth/domain/usecases/update_fcm_usecase.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/fcm_update/fcm_update_cubit.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/reset/reset_password_cubit.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/signup/signup_cubit.dart';
import 'package:gotcha_mfg_app/features/exercise/data/data_sources/exercise_data_source.dart';
import 'package:gotcha_mfg_app/features/exercise/data/repositories/exercise_repository.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/repositories/exercise_repository.dart';
import 'package:gotcha_mfg_app/features/exercise/data/models/get_exercise_response.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/update_series_use_case.dart';
import 'package:gotcha_mfg_app/features/exercise/domain/usecases/update_status_use_case.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/blocs/exercise/exercise_cubit.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/blocs/reflection_data/reflection_data_cubit.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/blocs/workout/workout_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/data_sources/home_data_source.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/check_in.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/delete_usecase.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/get_emotions.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/usecases/get_exercises.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/bottom_navbar/bottom_navbar_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in/check_in_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in_data/checkin_data_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/welcome_feed/welcome_feed_cubit.dart';
import 'package:gotcha_mfg_app/features/notification/data/data_sources/notification_datasource.dart';
import 'package:gotcha_mfg_app/features/notification/data/repositories/notification_repositoryimpl.dart';
import 'package:gotcha_mfg_app/features/notification/domain/repositories/notification_repository.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/get_notification_count_usecase.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/get_notification_usecase.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/post_notification.dart';
import 'package:gotcha_mfg_app/features/notification/domain/usecases/put_notification.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_cubit.dart';
import 'package:gotcha_mfg_app/features/onboarding/data/data_sources/onboarding_data_source.dart';
import 'package:gotcha_mfg_app/features/onboarding/data/repositories/onboarding_repository.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/repositories/onboarding_repository.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/get_onboarding.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/onboarding_detail_emotion.dart';
import 'package:gotcha_mfg_app/features/onboarding/domain/usecases/post_onboarding.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboard/onboarding_cubit.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboarding_data/onboarding_data_cubit.dart';
import 'package:gotcha_mfg_app/features/favourites/data/repositories/favourites_repository.dart';
import 'package:gotcha_mfg_app/features/favourites/domain/repositories/favourites_repository.dart';
import 'package:gotcha_mfg_app/features/favourites/domain/usecases/get_filtered_favourites.dart';
import 'package:gotcha_mfg_app/features/profile/domain/usecases/get_profile_details.dart';
import 'package:gotcha_mfg_app/features/profile/domain/usecases/logout_usecase.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/cubits/gym/gym_cubit.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/cubits/profile_links/profile_links_cubit.dart';
import 'package:gotcha_mfg_app/features/splash/data/data_sources/splash_remote_data_source.dart';
import 'package:gotcha_mfg_app/features/splash/data/repositories/splash_repository.dart';
import 'package:gotcha_mfg_app/features/splash/domain/repositories/splash_repository.dart';
import 'package:gotcha_mfg_app/features/splash/domain/usecases/info_usecase.dart';
import 'package:gotcha_mfg_app/features/splash/presentation/blocs/splash/splash_cubit.dart';
import 'package:gotcha_mfg_app/features/village/data/data_source/village_data_source.dart';
import 'package:gotcha_mfg_app/features/village/data/repositories/village_repositoryimpl.dart';
import 'package:gotcha_mfg_app/features/village/domain/repositories/village_repository.dart';
import 'package:gotcha_mfg_app/features/village/domain/usecases/add_village_user.dart';
import 'package:gotcha_mfg_app/features/village/domain/usecases/delete_village_usecase.dart';
import 'package:gotcha_mfg_app/features/village/domain/usecases/get_village_user.dart';
import 'package:gotcha_mfg_app/features/village/presentations/cubits/village/village_cubit.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'core/crashlytics_service.dart';
import 'core/mixpanel_service.dart';
import 'features/auth/data/data_sources/auth_remote_data_source.dart';
import 'features/auth/data/repositories/auth_repository_impl.dart';
import 'features/auth/domain/repositories/auth_repository.dart';
import 'features/auth/domain/usecases/delete_account_usecase.dart';
import 'features/auth/domain/usecases/get_identity_usecase.dart';
import 'features/auth/domain/usecases/login_usecase.dart';
import 'features/auth/presentation/blocs/login/login_cubit.dart';
import 'features/exercise/domain/usecases/get_workout_usecase.dart';
import 'features/exercise/domain/usecases/post_exercise_use_case.dart';
import 'features/explore/data/data_sources/explore_remote_data_source.dart';
import 'features/explore/data/repositories/explore_repository_impl.dart';
import 'features/explore/domain/repositories/explore_repository.dart';
import 'features/explore/domain/usecases/get_categories_use_case.dart';
import 'features/explore/domain/usecases/get_filtered_response_use_case.dart';
import 'features/explore/domain/usecases/get_workouts_use_case.dart';
import 'features/explore/presentation/cubits/explore_cubit.dart';
import 'features/favourites/domain/usecases/add_favourites_usecases.dart';
import 'features/favourites/domain/usecases/not_interest_usecase.dart';
import 'features/feedback/data/data_sources/feedback_datasource.dart';
import 'features/feedback/data/repositories/feedback_repository.dart';
import 'features/feedback/domain/repositories/feedback_repository.dart';
import 'features/feedback/domain/usecases/add_feedback_usecase.dart';
import 'features/feedback/presentation/blocs/feedback/feedback_cubit.dart';
import 'features/help_seeking/data/data_sources/help_seeking_datasource_impl.dart';
import 'features/help_seeking/data/data_sources/help_seeking_remote_datasource.dart';
import 'features/help_seeking/data/repositories/help_seeking_repository.dart';
import 'features/help_seeking/domain/repositories/help_seeking_repository.dart';
import 'features/help_seeking/domain/usecases/help_seeking_usecases.dart';
import 'features/help_seeking/presentation/cubits/help_seeking/help_seeking_cubit.dart';
import 'features/home/<USER>/usecases/continue_workout_usecase.dart';
import 'features/home/<USER>/usecases/get_detail_emotion.dart';
import 'features/home/<USER>/usecases/get_viewed_status_use_case.dart';
import 'features/home/<USER>/blocs/normal_feed/normal_feed_cubit.dart';
import 'features/favourites/data/data_sources/favourite_remote_data_source.dart';
import 'features/favourites/presentation/bloc/favourite/favourites_cubit.dart';
import 'features/poll/data/data_sources/gym_poll_datasource.dart';
import 'features/poll/data/repositories/gym_poll_repository.dart';
import 'features/poll/domain/repositories/gym_poll_repository.dart';
import 'features/poll/domain/usecases/add_gym_poll_usecase.dart';
import 'features/poll/domain/usecases/gym_poll_usecase.dart';
import 'features/poll/presentation/cubit/gym_poll/poll_cubit.dart';
import 'features/profile/data/data_sources/profile_remote_data_source.dart';
import 'features/profile/data/data_sources/profile_repository_impl.dart';
import 'features/profile/data/repositories/profile_repository.dart';
import 'features/profile/domain/usecases/get_gym_history.dart';
import 'features/profile/domain/usecases/get_profile_links_use_case.dart';
import 'features/profile/domain/usecases/update_profile_usecase.dart';
import 'features/profile/presentation/cubits/profile/profile_cubit.dart';
import 'features/quiz/data/data_sources/quiz_remote_datasource.dart';
import 'features/quiz/data/data_sources/quiz_repository_impl.dart';
import 'features/quiz/data/repositories/quiz_repository.dart';
import 'features/quiz/domain/usecases/assessment_usecase.dart';
import 'features/quiz/domain/usecases/post_assessment.dart';
import 'features/quiz/presentation/cubit/assessment/quiz_cubit.dart';
import 'features/village/domain/usecases/update_user_usecase.dart';

/// GetIt instance
final sl = GetIt.instance;

/// Initializes the dependency injection container by setting up
/// core storage dependencies and services.
Future<void> initLocator() async {
  sl.registerSingleton<AppRouter>(AppRouter());

  await _initializeStorage();
  await _initializeNetwork();
  await _initializeSplash();
  await _initializeOnboarding();
  await _initializeHome();
  await _initializeExercise();
  await _initializeDiscover();
  await _initializeProfile();
  await _initializeFavourite();
  await _initializeAuth();
  await _initializeQuiz();
  await _initializeHelpSeeking();
  await _initializeVillage();
  await _initializePoll();
  await _initializeFeedback();
  await _initializeNotification();
}

Future<void> _initializeStorage() async {
  /// Initialize SharedPreferences
  final sharedPrefs = await SharedPreferences.getInstance();

  /// Create an instance of FlutterSecureStorage
  const secureStorage = FlutterSecureStorage();

  sl
    ..registerSingleton<SharedPreferences>(sharedPrefs)
    ..registerLazySingleton<FlutterSecureStorage>(() => secureStorage)
    ..registerSingleton<StorageService>(
      StorageServiceImpl(
        sl<FlutterSecureStorage>(),
        sl<SharedPreferences>(),
      ),
    );
}

Future<void> _initializeNetwork() async {
  /// Register network dependencies
  final internetConnection = InternetConnection.createInstance();
  sl
    ..registerLazySingleton<InternetConnection>(
      () => internetConnection,
    )
    ..registerLazySingleton<NetworkInfo>(
      () => NetworkInfoImpl(sl()),
    )
    ..registerLazySingleton<TokenManager>(
      () => TokenManagerImpl(sl()),
    )
    ..registerLazySingleton<NetworkClient>(
      () => NetworkClient(
        config: NetworkConfig.production(),
        tokenManager: sl(),
        networkInfo: sl(),
      ),
    )
    ..registerLazySingleton<Dio>(
      () => sl<NetworkClient>().instance,
    );
}

Future<void> initMixpanel(String mixpanelToken) async {
  // Takes token as argument
  sl.registerSingletonAsync<MixpanelService>(() async {
    final service = MixpanelService();
    await service.init(mixpanelToken); // Use the passed token
    return service;
  });
  await sl.allReady();
}

Future<void> initCrashlytics() async {
  sl.registerSingleton<CrashlyticsService>(CrashlyticsService());
}

Future<void> _initializeSplash() async {
  sl.registerLazySingleton<SplashRemoteDataSource>(
      () => SplashRemoteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<SplashRepository>(
      () => SplashRepositoryImpl(sl<SplashRemoteDataSource>()));
  // sl.registerLazySingleton<OnboardingUsecase>(
  //     () => OnboardingUsecase(sl<SplashRepository>()));
  sl.registerLazySingleton<SplashCubit>(
      () => SplashCubit(InfoUseCase(sl<SplashRepository>())
          // sl<OnboardingUsecase>(),
          ));
}

Future<void> _initializeHome() async {
  sl.registerLazySingleton<HomeDataSource>(() => HomeDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<HomeRepository>(
      () => HomeRepositoryImpl(sl<HomeDataSource>()));
  sl.registerLazySingleton<GetExercisesUsecase>(
      () => GetExercisesUsecase(sl<HomeRepository>()));
  sl.registerLazySingleton<GetViewedStatusUseCase>(
      () => GetViewedStatusUseCase(sl<HomeRepository>()));
  sl.registerLazySingleton<WelcomeFeedCubit>(() => WelcomeFeedCubit(
        sl<GetExercisesUsecase>(),
        sl<GetViewedStatusUseCase>(),
      ));
  sl.registerLazySingleton<GetEmotionsUsecase>(
      () => GetEmotionsUsecase(sl<HomeRepository>()));
  sl.registerLazySingleton<CheckInUsecase>(
      () => CheckInUsecase(sl<HomeRepository>()));
  sl.registerLazySingleton<DeleteCheckInUseCase>(
      () => DeleteCheckInUseCase(sl<HomeRepository>()));
  sl.registerLazySingleton<GetRecentUncompletedWorkoutUseCase>(
      () => GetRecentUncompletedWorkoutUseCase(sl<HomeRepository>()));

  sl.registerLazySingleton<NormalFeedCubit>(() => NormalFeedCubit(
      sl<GetEmotionsUsecase>(),
      sl<GetDetailEmotionsUsecase>(),
      sl<GetExercisesUsecase>(),
      sl<GetRecentUncompletedWorkoutUseCase>()));
  sl.registerLazySingleton<GetDetailEmotionsUsecase>(
      () => GetDetailEmotionsUsecase(sl<HomeRepository>()));
  sl.registerLazySingleton<CheckInCubit>(() => CheckInCubit(
        sl<GetDetailEmotionsUsecase>(),
        sl<CheckInUsecase>(),
        sl<DeleteCheckInUseCase>(),
      ));
  sl.registerLazySingleton<BottomNavbarCubit>(() => BottomNavbarCubit());

  sl.registerLazySingleton<CheckinDataCubit>(() => CheckinDataCubit());
}

Future<void> _initializeDiscover() async {
  sl.registerLazySingleton<ExploreRemoteDataSource>(
    () => ExploreRemoteDataSourceImpl(sl<Dio>()),
  );
  sl.registerLazySingleton<ExploreRepository>(
    () => ExploreRepositoryImpl(sl<ExploreRemoteDataSource>()),
  );
  sl.registerLazySingleton<GetFilteredExercisesUseCase>(
    () => GetFilteredExercisesUseCase(sl<ExploreRepository>()),
  );
  sl.registerLazySingleton<GetCategoriesUseCase>(
    () => GetCategoriesUseCase(sl<ExploreRepository>()),
  );
  sl.registerLazySingleton<GetWorkoutsUseCase>(
    () => GetWorkoutsUseCase(sl<ExploreRepository>()),
  );
  sl.registerLazySingleton<ExploreCubit>(
    () => ExploreCubit(
      sl<GetCategoriesUseCase>(),
      sl<GetFilteredExercisesUseCase>(),
      sl<GetWorkoutsUseCase>(),
    ),
  );
}

Future<void> _initializeExercise() async {
  sl.registerLazySingleton<ExerciseDataSource>(
      () => ExerciseDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<ExerciseRepository>(
    () => ExerciseRepositoryImpl(sl<ExerciseDataSource>()),
  );
  sl.registerLazySingleton<GetDetailExerciseUsecase>(
      () => GetDetailExerciseUsecase(sl<ExerciseRepository>()));
  sl.registerLazySingleton<PostDetailExerciseUsecase>(
      () => PostDetailExerciseUsecase(sl<ExerciseRepository>()));
  sl.registerLazySingleton<GetSingleWorkoutUsecase>(
      () => GetSingleWorkoutUsecase(sl<ExerciseRepository>()));
  sl.registerLazySingleton<UpdateExerciseStatusUseCase>(
      () => UpdateExerciseStatusUseCase(sl<ExerciseRepository>()));
  sl.registerLazySingleton<UpdateSeriesStatusUseCase>(
      () => UpdateSeriesStatusUseCase(sl<ExerciseRepository>()));
  sl.registerLazySingleton<ExerciseCubit>(() => ExerciseCubit(
        sl<GetDetailExerciseUsecase>(),
        sl<PostDetailExerciseUsecase>(),
        sl<UpdateExerciseStatusUseCase>(),
        sl<UpdateSeriesStatusUseCase>(),
        sl<AddFavoritesUseCase>(),
      ));
  sl.registerLazySingleton<WorkoutCubit>(() => WorkoutCubit(
        sl<GetSingleWorkoutUsecase>(),
      ));
  sl.registerLazySingleton<ReflectionDataCubit>(() => ReflectionDataCubit());
}

Future<void> _initializeOnboarding() async {
  sl.registerLazySingleton<OnBoardingDataSource>(
      () => OnboardingDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<OnBoardingRepository>(
      () => OnboardRepositoryImpl(sl<OnBoardingDataSource>()));
  sl.registerLazySingleton<GetOnboardingUsecase>(
      () => GetOnboardingUsecase(sl<OnBoardingRepository>()));
  sl.registerLazySingleton<OnboardingDetailEmotionsUsecase>(
      () => OnboardingDetailEmotionsUsecase(sl<OnBoardingRepository>()));
  sl.registerLazySingleton<PostOnboardingUsecase>(
      () => PostOnboardingUsecase(sl<OnBoardingRepository>()));
  sl.registerLazySingleton<OnboardingCubit>(() => OnboardingCubit(
        sl<OnboardingDetailEmotionsUsecase>(),
        sl<GetOnboardingUsecase>(),
        sl<PostOnboardingUsecase>(),
      ));
  sl.registerLazySingleton<OnboardingDataCubit>(() => OnboardingDataCubit());
}

Future<void> _initializeFavourite() async {
  sl.registerLazySingleton<FavouriteRemoteDataSource>(
      () => FavouriteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<FavouritesRepository>(
      () => FavouriteRepositoryImpl(sl<FavouriteRemoteDataSource>()));
  sl.registerLazySingleton<GetFilteredFavouriteUseCase>(
      () => GetFilteredFavouriteUseCase(sl<FavouritesRepository>()));
  sl.registerLazySingleton<AddFavoritesUseCase>(
      () => AddFavoritesUseCase(sl<FavouritesRepository>()));
  sl.registerLazySingleton<NotInterestUseCase>(
      () => NotInterestUseCase(sl<FavouritesRepository>()));

  sl.registerLazySingleton<FavouritesCubit>(() => FavouritesCubit(
        sl<GetFilteredFavouriteUseCase>(),
        sl<AddFavoritesUseCase>(),
        sl<NotInterestUseCase>(),
      ));
}

Future<void> _initializeProfile() async {
  sl.registerLazySingleton<ProfileRemoteDataSource>(
      () => ProfileRemoteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<ProfileRepository>(
      () => ProfileRepositoryImpl(sl<ProfileRemoteDataSource>()));
  sl.registerLazySingleton<GetProfileLinksUseCase>(
      () => GetProfileLinksUseCase(sl<ProfileRepository>()));
  sl.registerLazySingleton<GetProfileDetailUseCase>(
      () => GetProfileDetailUseCase(sl<ProfileRepository>()));
  sl.registerLazySingleton<UpdateProfileUseCase>(
      () => UpdateProfileUseCase(sl<ProfileRepository>()));
  sl.registerLazySingleton<LogoutUseCase>(
      () => LogoutUseCase(sl<ProfileRepository>()));
  sl.registerLazySingleton<GetNotificationCountUseCase>(
      () => GetNotificationCountUseCase(sl<NotificationRepository>()));
  sl.registerLazySingleton<GetNotificationUseCase>(
      () => GetNotificationUseCase(sl<NotificationRepository>()));

  sl.registerLazySingleton<ProfileCubit>(() => ProfileCubit(
        sl<GetProfileLinksUseCase>(),
        sl<GetProfileDetailUseCase>(),
        sl<GetIdentityGroupsUseCase>(),
        sl<UpdateProfileUseCase>(),
        sl<LogoutUseCase>(),
        sl<GetNotificationCountUseCase>(),
        sl<GetNotificationUseCase>(),
      ));

  sl.registerLazySingleton<ProfileLinksCubit>(
    () => ProfileLinksCubit(
      sl<GetProfileLinksUseCase>(),
      sl<GetProfileDetailUseCase>(),
      sl<GetIdentityGroupsUseCase>(),
      sl<UpdateProfileUseCase>(),
      sl<GetNotificationCountUseCase>(),
      sl<GetNotificationUseCase>(),
    ),
  );
  sl.registerLazySingleton<GymCubit>(
    () => GymCubit(GetGymHistoryUseCase(sl<ProfileRepository>())),
  );
}

Future<void> _initializeAuth() async {
  sl.registerLazySingleton<AuthRemoteDataSource>(
      () => AuthRemoteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<AuthRepository>(
      () => AuthRepositoryImpl(sl<AuthRemoteDataSource>()));
  sl.registerLazySingleton<AuthCubit>(() => AuthCubit(
      LoginUseCase(sl<AuthRepository>()), DeleteUsecase(sl<AuthRepository>())));
  sl.registerLazySingleton<SignupCubit>(
      () => SignupCubit(SignUpUseCase(sl<AuthRepository>())));
  sl.registerLazySingleton<GetIdentityGroupsUseCase>(
      () => GetIdentityGroupsUseCase(sl<AuthRepository>()));
  sl.registerLazySingleton<ResetCubit>(
      () => ResetCubit(ForgotPasswordUseCase(sl<AuthRepository>())));
  sl.registerLazySingleton<FcmUpdateCubit>(
      () => FcmUpdateCubit(UpdateFcmTokenUseCase(sl<AuthRepository>())));
}

Future<void> _initializeQuiz() async {
  sl.registerLazySingleton<QuizRemoteDataSource>(
      () => QuizRemoteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<QuizRepository>(
      () => QuizRepositoryImpl(sl<QuizRemoteDataSource>()));
  sl.registerLazySingleton<QuizCubit>(() => QuizCubit(
      GetQuestionsUseCase(sl<QuizRepository>()),
      PostAssessmentUseCase(sl<QuizRepository>())));
}

Future<void> _initializeHelpSeeking() async {
  sl.registerLazySingleton<HelpSeekingRemoteDataSource>(
      () => HelpSeekingRemoteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<HelpSeekingRepository>(
      () => HelpSeekingRepositoryImpl(sl<HelpSeekingRemoteDataSource>()));
  sl.registerLazySingleton<HelpSeekingCubit>(() => HelpSeekingCubit(
      GetHelpSeekingPathwaysUseCase(sl<HelpSeekingRepository>())));
}

// locator.dart
Future<void> _initializeVillage() async {
  sl.registerLazySingleton<VillageRemoteDataSource>(
      () => VillageRemoteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<VillageRepository>(
      () => VillageRepositoryImpl(sl<VillageRemoteDataSource>()));
  sl.registerLazySingleton<VillageCubit>(() => VillageCubit(
        GetVillageUserUseCase(sl<VillageRepository>()),
        AddVillageUserUseCase(sl<VillageRepository>()),
        UpdateVillageUserUseCase(sl<VillageRepository>()),
        DeleteVillageUserUseCase(sl<VillageRepository>()),
      ));
} // Method to use in locator.dart

// locator.dart
Future<void> _initializePoll() async {
  sl.registerLazySingleton<PollRemoteDataSource>(
      () => PollRemoteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<PollRepository>(
      () => PollRepositoryImpl(sl<PollRemoteDataSource>()));
  sl.registerLazySingleton<PollCubit>(() => PollCubit(
      GetGymPollMessageUseCase(sl<PollRepository>()),
      AddGymPollUseCase(sl<PollRepository>())));
}

Future<void> _initializeFeedback() async {
  sl.registerLazySingleton<FeedbackRemoteDataSource>(
      () => FeedbackRemoteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<FeedbackRepository>(
      () => FeedbackRepositoryImpl(sl<FeedbackRemoteDataSource>()));
  sl.registerLazySingleton<FeedbackCubit>(
      () => FeedbackCubit(AddFeedbackUseCase(sl<FeedbackRepository>())));
}

Future<void> _initializeNotification() async {
  sl.registerLazySingleton<NotificationRemoteDataSource>(
      () => NotificationRemoteDataSourceImpl(sl<Dio>()));
  sl.registerLazySingleton<NotificationRepository>(
      () => NotificationRepositoryImpl(sl<NotificationRemoteDataSource>()));

  sl.registerLazySingleton<NotificationCubit>(() => NotificationCubit(
        GetNotificationUseCase(sl<NotificationRepository>()),
        PutNotificationUseCase(sl<NotificationRepository>()),
        PostNotificationReminderTimeUseCase(sl<NotificationRepository>()),
        GetNotificationCountUseCase(sl<NotificationRepository>()),
      ));
}
