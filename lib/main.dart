// main.dart

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/config/router/app_router.dart';
import 'package:gotcha_mfg_app/config/theme/app_theme.dart';
import 'package:gotcha_mfg_app/core/firebase_push.dart';
import 'package:gotcha_mfg_app/core/utils/ios_data_cleaner.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/features/auth/presentation/blocs/reset/reset_password_cubit.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/blocs/exercise/exercise_cubit.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/blocs/reflection_data/reflection_data_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/bottom_navbar/bottom_navbar_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in/check_in_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/check_in_data/checkin_data_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/normal_feed/normal_feed_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/welcome_feed/welcome_feed_cubit.dart';
import 'package:gotcha_mfg_app/features/notification/presentation/blocs/notification/notification_cubit.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboard/onboarding_cubit.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboarding_data/onboarding_data_cubit.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/cubits/gym/gym_cubit.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/cubits/profile_links/profile_links_cubit.dart';
import 'package:gotcha_mfg_app/features/splash/presentation/blocs/splash/splash_cubit.dart';
import 'package:gotcha_mfg_app/firebase_options.dart';
import 'package:gotcha_mfg_app/locator.dart';
import 'package:shared_preference_app_group/shared_preference_app_group.dart';
import 'features/auth/presentation/blocs/fcm_update/fcm_update_cubit.dart';
import 'features/auth/presentation/blocs/login/login_cubit.dart';
import 'features/auth/presentation/blocs/signup/signup_cubit.dart';
import 'features/exercise/presentation/blocs/workout/workout_cubit.dart';
import 'features/explore/presentation/cubits/explore_cubit.dart';
import 'features/favourites/presentation/bloc/favourite/favourites_cubit.dart';
import 'features/feedback/presentation/blocs/feedback/feedback_cubit.dart';
import 'features/help_seeking/presentation/cubits/help_seeking/help_seeking_cubit.dart';
import 'features/poll/presentation/cubit/gym_poll/poll_cubit.dart';
import 'features/profile/presentation/cubits/profile/profile_cubit.dart';
import 'features/quiz/presentation/cubit/assessment/quiz_cubit.dart';
import 'features/village/presentations/cubits/village/village_cubit.dart';

var devMixpanelToken = "48c7664aae171aa3efdd834f1d54cd4a";
var prodMixpanelToken = "24008781aab90caae853d9e21e63da6e";
var vbsMixpanelToken = "785156e0e35bbc94f5c11ef89fa125ad";

/// The main entry point of the application.
///
/// This function ensures that the Flutter framework is initialized,
/// sets up dependency injection, and starts the application by
/// running [MyApp].
/// // In a central location like main.dart or in your push notification file
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  if (isIos) {
    await SharedPreferenceAppGroup.setAppGroup(
        'group.com.gotcha4life.mentalfitnessgym');
  }

  // dev
  // await initMixpanel("48c7664aae171aa3efdd834f1d54cd4a");

  // prod
  // await initMixpanel("24008781aab90caae853d9e21e63da6e");

  // vbs
  // await initMixpanel("785156e0e35bbc94f5c11ef89fa125ad");

  // new
  await initMixpanel(prodMixpanelToken);

  await initLocator();

  await configurePushNotifications(); // Initialize push notifications

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    // DeviceOrientation.landscapeRight,
    // DeviceOrientation.landscapeRight,
  ]);
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  await initCrashlytics();
  FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: false);
    return true;
  };
  appFirstInstallCheck();
  runApp(const MyApp());
}

// Separate function to configure push notifications
Future<void> configurePushNotifications() async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  await MFGPushNotification.initListeners();
}

/// The root widget of the app.
///
/// This widget creates a [MaterialApp] which is configured to use the
/// [AppRouter] for navigation and the [NavObserver] for observing route changes.
/// It also sets the title of the app, disables the debug banner, and sets the
/// theme to [AppTheme.light].
class MyApp extends StatelessWidget {
  /// Creates a new instance of [MyApp].
  ///
  /// The [key] parameter is optional and is used to identify the widget in the
  /// widget tree.
  const MyApp({super.key});

  /// The router used by the app.
  // final _appRouter = AppRouter();

  @override

  /// The build method of [MyApp].
  ///
  /// This method is called when the widget is first created and whenever the
  /// widget's dependencies change.
  ///
  /// It returns a [MaterialApp] which is configured to use the [AppRouter]
  /// for navigation and the [NavObserver] for observing route changes.
  /// It also sets the title of the app, disables the debug banner, and sets the
  /// theme to [AppTheme.light].
  Widget build(BuildContext context) {
    final appRouter = sl<AppRouter>();
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => sl<SplashCubit>()),
        BlocProvider(create: (_) => sl<WelcomeFeedCubit>()),
        BlocProvider(create: (_) => sl<NormalFeedCubit>()),
        BlocProvider(create: (_) => sl<CheckInCubit>()),
        BlocProvider(create: (_) => sl<OnboardingCubit>()),
        BlocProvider(create: (_) => sl<OnboardingDataCubit>()),
        BlocProvider(create: (_) => sl<BottomNavbarCubit>()),
        BlocProvider(create: (_) => sl<CheckinDataCubit>()),
        BlocProvider(create: (_) => sl<ExerciseCubit>()),
        BlocProvider(create: (_) => sl<ExploreCubit>()),
        BlocProvider(create: (_) => sl<ReflectionDataCubit>()),
        BlocProvider(create: (_) => sl<FavouritesCubit>()),
        BlocProvider(create: (_) => sl<WorkoutCubit>()),
        BlocProvider(create: (_) => sl<ProfileCubit>()),
        BlocProvider(create: (_) => sl<ProfileLinksCubit>()),
        BlocProvider(create: (_) => sl<AuthCubit>()),
        BlocProvider(create: (_) => sl<SignupCubit>()),
        BlocProvider(create: (_) => sl<GymCubit>()),
        BlocProvider(create: (_) => sl<QuizCubit>()),
        BlocProvider(create: (_) => sl<HelpSeekingCubit>()),
        BlocProvider(create: (_) => sl<VillageCubit>()),
        BlocProvider(create: (_) => sl<PollCubit>()),
        BlocProvider(create: (_) => sl<FeedbackCubit>()),
        BlocProvider(create: (_) => sl<ResetCubit>()),
        BlocProvider(create: (_) => sl<NotificationCubit>()),
        BlocProvider(create: (_) => sl<FcmUpdateCubit>()),
      ],
      child: MaterialApp.router(
        routerConfig: appRouter.config(),
        title: 'MFGym',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.light,
        // scrollBehavior: const ScrollBehavior().copyWith(
        //   physics: const ClampingScrollPhysics(),
        // ),
        scrollBehavior: AppScrollBehavior(),
      ),
    );
  }
}

class AppScrollBehavior extends CupertinoScrollBehavior {
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return AppScrollPhysics();
  }

  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    return StretchingOverscrollIndicator(
      axisDirection: details.direction,
      child: child,
    );
  }
}

class AppScrollPhysics extends ClampingScrollPhysics {
  @override
  Simulation? createBallisticSimulation(
      ScrollMetrics position, double velocity) {
    final Tolerance tolerance = toleranceFor(position);
    if (velocity.abs() >= tolerance.velocity || position.outOfRange) {
      return BouncingScrollSimulation(
        spring: spring,
        position: position.pixels,
        velocity: velocity,
        leadingExtent: position.minScrollExtent,
        trailingExtent: position.maxScrollExtent,
        tolerance: tolerance,
      );
    }
    return null;
  }
}
