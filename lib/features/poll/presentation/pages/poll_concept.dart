import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/app_print.dart';
import 'package:gotcha_mfg_app/features/poll/data/models/add_gym_poll.dart';
import 'package:gotcha_mfg_app/locator.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import '../../../../config/theme/app_colors.dart';

import '../../../../core/mixpanel_service.dart';
import '../cubit/gym_poll/poll_cubit.dart';
import '../cubit/gym_poll/poll_state.dart'; // Import PollState

class MessageBottomSheet extends StatefulWidget {
  final String senderName;
  final String recipientName;
  final String message;
  final String emote;
  final bool showSecond;
  final LinearGradient grade;

  const MessageBottomSheet({
    super.key,
    required this.senderName,
    required this.recipientName,
    required this.message,
    required this.emote,
    required this.showSecond,
    required this.grade,
  });

  @override
  State<MessageBottomSheet> createState() => _MessageBottomSheetState();
}

class _MessageBottomSheetState extends State<MessageBottomSheet> {
  bool showMessageOptions = false;
  bool showConfirmation = false;
  String? selectedMessage;

  /// Generates a random linear gradient with 2 colors

  /// Creates a CircleAvatar with a random gradient background
  Widget gradientCircleAvatar({
    required String text,
    double radius = 16,
    TextStyle? textStyle,
  }) {
    return ShaderMask(
      shaderCallback: (bounds) => widget.grade.createShader(bounds),
      child: CircleAvatar(
        backgroundColor: Colors.white,
        radius: radius,
        child: Text(
          text,
          style: textStyle ??
              const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
        ),
      ),
    );
  }

  String? senderName;
  final List<String> messageOptions = [
    "Showing up today is already a win.",
    "Hope today brings something good your way.",
    "Keep going - what you’re doing matters.",
  ];
  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Gym Poll Page',
      properties: {'Code': 'screen_view.gym_poll_page'},
    );
    widget.showSecond == true ? showMessageOptions = true : false;
    // Fetch Gym Poll Message when the widget initializes
    context.read<PollCubit>().getGymPollMessage();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);

    return BlocConsumer<PollCubit, PollState>(
      listener: (context, state) {
        // Optional: You can add listeners here if needed.
        // For example, you might show a SnackBar if there's an error.
        if (state is PollError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: ${state.message}')),
          );
        }
      },
      builder: (context, pollState) {
        // Handle the PollCubit states
        if (pollState is PollLoading) {
          return Container(
            height: size.height / 2, // Occupy half the screen

            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(0)),
            ),
            child: const LoadingWidget(
              color: Colors.white,
            ),
          );
        } else if (pollState is PollError) {
          return Container(
            height: size.height / 2, // Occupy half the screen
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(0)),
            ),
            child: Center(child: Text('Error: ${pollState.message}')),
          );
        } else if (pollState is PollLoaded) {
          // Access the data from PollLoaded state
          final gymPollMessage =
              pollState.gymPollResponse.data?.message ?? 'N/A';
          senderName = pollState.gymPollResponse.data?.sender?.firstName;

          return _buildMainContent(context, textTheme, size, gymPollMessage);
        } else if (pollState is PollAddSuccess) {
          final gymPollMessage =
              pollState.gymPollResponse.data?.message ?? 'N/A';
          // Access the data from PollLoaded state
          showConfirmation = true;
          return _buildMainContent(context, textTheme, size, gymPollMessage);
        }

        return const SizedBox.shrink(); // Initial or other states
      },
    );
  }

  Widget _buildMainContent(
    BuildContext context,
    TextTheme textTheme,
    Size size,
    String? gymPollMessage,
  ) {
    if (showConfirmation) {
      return Container(
        height: size.height / 2,
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(0)),
        ),
        child: Column(
          children: [
            const Spacer(flex: 2),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                "Message sent - you just made someone's day!",
                textAlign: TextAlign.center,
                style: textTheme.sectionHeading.copyWith(
                  color: AppColors.navy,
                ),
              ),
            ),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Expanded(
                    child: SizedBox(
                      child: CustomPaint(
                        painter: RPSCustomPainterRight(),
                        child: Container(
                          // Remove Expanded
                          // width: size.width * 0.65,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: Center(
                            child: Text(
                              '${selectedMessage ?? ""} (${widget.senderName.isEmpty ? "Anonymous" : capitalizeFirstLetter(widget.senderName)})',
                              style: textTheme.labels.copyWith(
                                color: AppColors.navy,
                              ),
                              maxLines: 3,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const Gap(12),
                  gradientCircleAvatar(
                    text: widget.senderName.isEmpty
                        ? 'A'
                        : widget.senderName.substring(0, 1),
                    radius: 16,
                    textStyle: textTheme.sectionHeading.copyWith(
                      color: AppColors.navy,
                    ),
                  ),
                  // GradientCircleAvatar(
                  //   text: 'A',
                  //   radius: 16,
                  // ),
                ],
              ),
            ),
            const Spacer(),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 40.0),
              child: SizedBox(
                width: double.infinity,
                height: 40,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    backgroundColor: AppColors.coral,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                  ),
                  child: Text(
                    'Done',
                    style: textTheme.linkText.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
            const Spacer(flex: 3),
          ],
        ),
      );
    }

    if (showMessageOptions) {
      return Container(
        height: size.height / 2,
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 8),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(0)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Gap(4),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: Text(
                'Share a word of encouragement',
                textAlign: TextAlign.center,
                style: textTheme.sectionHeading.copyWith(
                  color: AppColors.navy,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(0, 4, 0, 4),
              child: Text(
                'Pass on a little encouragement to someone else in the Gym.',
                textAlign: TextAlign.center,
                style: textTheme.placeholder.copyWith(
                  color: AppColors.navy,
                ),
              ),
            ),
            const Spacer(),
            Expanded(
              flex: 24,
              child: Scrollbar(
                // thumbVisibility: true,
                child: ListView.separated(
                  shrinkWrap: true,
                  padding: const EdgeInsets.only(top: 8),
                  itemCount: messageOptions.length,
                  separatorBuilder: (context, index) => const Gap(16),
                  itemBuilder: (context, index) {
                    final msg = messageOptions[index];
                    return Padding(
                      padding: const EdgeInsets.only(left: 24, right: 24),
                      child: InkWell(
                        onTap: () {
                          var params = AddGymPoll(message: msg);
                          context.read<PollCubit>().addGymPoll(params);
                          setState(() {
                            selectedMessage = msg;
                          });
                          // mixpanel
                          sl<MixpanelService>().trackButtonClick(
                              'Share a Word of Encouragement',
                              properties: {
                                'Page': 'Gym Poll Page',
                                'Code':
                                    'click.gym_poll_page.share_a_word_of_encouragement'
                              });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 20,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.lightRed,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: Text(
                            msg,
                            style: textTheme.linkText.copyWith(
                              color: AppColors.navy,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            // const Spacer(),
            // const Gap(4),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(
                'Maybe later',
                style: textTheme.ralewayBold.copyWith(
                  fontSize: 13,
                  color: AppColors.coral,
                ),
              ),
            ),
            const Spacer(flex: 6),
            // const Gap(8),
          ],
        ),
      );
    }

    return Container(
      height: size.height / 2,
      padding: const EdgeInsets.fromLTRB(16, 20, 20, 20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(0)),
      ),
      child: Column(
        children: [
          const Spacer(
            flex: 1,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              'A little kindness just dropped in',
              textAlign: TextAlign.center,
              style: textTheme.sectionHeading.copyWith(
                color: AppColors.navy,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(0, 2, 0, 16),
            child: Text(
              'Someone in the Gym has sent you some encouragement.',
              textAlign: TextAlign.center,
              style: textTheme.placeholder.copyWith(
                color: AppColors.navy,
              ),
            ),
          ),
          const Gap(16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.only(top: 8),
                  child: gradientCircleAvatar(
                    text:
                        senderName == null ? 'A' : senderName!.substring(0, 1),
                    radius: 16,
                    textStyle: textTheme.sectionHeading.copyWith(
                      color: AppColors.navy,
                    ),
                  ),
                ),
                const Gap(12),
                Expanded(
                  child: SizedBox(
                    // Wrap with SizedBox and provide width constraint
                    // width: size.width, // Adjust the width as needed
                    child: CustomPaint(
                      painter: RPSCustomPainterLeft(),
                      child: Container(
                        // Remove Expanded
                        padding: const EdgeInsets.only(
                            top: 12, bottom: 12, left: 32),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: Text(
                          gymPollMessage ?? 'N/A',
                          style: textTheme.labels.copyWith(
                            color: AppColors.navy,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Gap(32),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: SizedBox(
              width: size.width,
              height: 40,
              child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    showMessageOptions = true;
                  });
                  // mixpanel
                  sl<MixpanelService>().trackButtonClick(
                      'Keep the Message Chain Going',
                      properties: {
                        'Page': 'Gym Poll Page',
                        'Code':
                            'click.gym_poll_page.keep_the_message_chain_going'
                      });
                },
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  backgroundColor: AppColors.coral,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'Keep the message chain going',
                    style: textTheme.linkText.copyWith(
                      color: Colors.white,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              'Maybe later',
              style: textTheme.linkText.copyWith(
                color: AppColors.coral,
              ),
            ),
          ),
          const Spacer(
            flex: 2,
          )
        ],
      ),
    );
  }
}

// Example usage:
void showMessageSheet(
  BuildContext context,
  String? emote,
  String? name,
  LinearGradient gradient,
) {
  info('emote----$emote');
  var size = MediaQuery.of(context).size;
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    barrierColor: Colors.black45,
    constraints: BoxConstraints(maxWidth: size.width),
    builder: (context) => MessageBottomSheet(
      showSecond: (emote == 'negative' || emote == 'neutral') ? false : true,
      emote: emote ?? 'N/A',
      senderName: name != null ? capitalizeFirstLetter(name) : 'Anonymous',
      recipientName: 'Connor',
      message: 'N/A',
      grade: gradient,
    ),
  );
}

String capitalizeFirstLetter(String text) {
  if (text.isEmpty) return text;
  return text[0].toUpperCase() + text.substring(1);
}

//Copy this CustomPainter code to the Bottom of the File
class RPSCustomPainterLeft extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Path path_0 = Path();
    path_0.moveTo(size.width * 0.03453718, size.height * 0.2592593);
    path_0.cubicTo(
        size.width * 0.03453718,
        size.height * 0.1237450,
        size.width * 0.05988846,
        size.height * 0.01388889,
        size.width * 0.09116111,
        size.height * 0.01388889);
    path_0.lineTo(size.width * 0.9401709, size.height * 0.01388889);
    path_0.cubicTo(
        size.width * 0.9714444,
        size.height * 0.01388889,
        size.width * 0.9967949,
        size.height * 0.1237450,
        size.width * 0.9967949,
        size.height * 0.2592593);
    path_0.lineTo(size.width * 0.9967949, size.height * 0.7407407);
    path_0.cubicTo(
        size.width * 0.9967949,
        size.height * 0.8762556,
        size.width * 0.9714444,
        size.height * 0.9861111,
        size.width * 0.9401709,
        size.height * 0.9861111);
    path_0.lineTo(size.width * 0.06411581, size.height * 0.9861111);
    path_0.cubicTo(
        size.width * 0.04777991,
        size.height * 0.9861111,
        size.width * 0.03453718,
        size.height * 0.9287259,
        size.width * 0.03453718,
        size.height * 0.8579370);
    path_0.lineTo(size.width * 0.03453718, size.height * 0.6831685);
    path_0.cubicTo(
        size.width * 0.03453718,
        size.height * 0.6379333,
        size.width * 0.02310714,
        size.height * 0.6101519,
        size.width * 0.01419936,
        size.height * 0.6337370);
    path_0.cubicTo(
        size.width * 0.007915000,
        size.height * 0.6503778,
        size.width * 0.0007529829,
        size.height * 0.6188333,
        size.width * 0.004694316,
        size.height * 0.5918759);
    path_0.lineTo(size.width * 0.03070449, size.height * 0.4139648);
    path_0.cubicTo(
        size.width * 0.03320799,
        size.height * 0.3968426,
        size.width * 0.03453718,
        size.height * 0.3769870,
        size.width * 0.03453718,
        size.height * 0.3567167);
    path_0.lineTo(size.width * 0.03453718, size.height * 0.2592593);
    path_0.close();

    Paint paint0Stroke = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.006410256;
    paint0Stroke.color = const Color(0xffEAEEF5).withOpacity(1.0);
    canvas.drawPath(path_0, paint0Stroke);

    Paint paint0Fill = Paint()..style = PaintingStyle.fill;
    paint0Fill.color = Colors.white.withOpacity(1.0);
    canvas.drawPath(path_0, paint0Fill);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

//Copy this CustomPainter code to the Bottom of the File
class RPSCustomPainterRight extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Path path_0 = Path();
    path_0.moveTo(size.width * 0.9654615, size.height * 0.2592593);
    path_0.cubicTo(
        size.width * 0.9654615,
        size.height * 0.1237450,
        size.width * 0.9401111,
        size.height * 0.01388889,
        size.width * 0.9088376,
        size.height * 0.01388889);
    path_0.lineTo(size.width * 0.05982906, size.height * 0.01388889);
    path_0.cubicTo(
        size.width * 0.02855650,
        size.height * 0.01388889,
        size.width * 0.003205128,
        size.height * 0.1237450,
        size.width * 0.003205128,
        size.height * 0.2592593);
    path_0.lineTo(size.width * 0.003205128, size.height * 0.7407407);
    path_0.cubicTo(
        size.width * 0.003205128,
        size.height * 0.8762556,
        size.width * 0.02855650,
        size.height * 0.9861111,
        size.width * 0.05982906,
        size.height * 0.9861111);
    path_0.lineTo(size.width * 0.9358846, size.height * 0.9861111);
    path_0.cubicTo(
        size.width * 0.9522179,
        size.height * 0.9861111,
        size.width * 0.9654615,
        size.height * 0.9287259,
        size.width * 0.9654615,
        size.height * 0.8579370);
    path_0.lineTo(size.width * 0.9654615, size.height * 0.6831685);
    path_0.cubicTo(
        size.width * 0.9654615,
        size.height * 0.6379333,
        size.width * 0.9768932,
        size.height * 0.6101519,
        size.width * 0.9857991,
        size.height * 0.6337370);
    path_0.cubicTo(
        size.width * 0.9920855,
        size.height * 0.6503778,
        size.width * 0.9992479,
        size.height * 0.6188333,
        size.width * 0.9953077,
        size.height * 0.5918759);
    path_0.lineTo(size.width * 0.9692949, size.height * 0.4139648);
    path_0.cubicTo(
        size.width * 0.9667906,
        size.height * 0.3968426,
        size.width * 0.9654615,
        size.height * 0.3769870,
        size.width * 0.9654615,
        size.height * 0.3567167);
    path_0.lineTo(size.width * 0.9654615, size.height * 0.2592593);
    path_0.close();

    Paint paint0Stroke = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.006410256;
    paint0Stroke.color = const Color(0xffEAEEF5).withOpacity(1.0);
    canvas.drawPath(path_0, paint0Stroke);

    Paint paint0Fill = Paint()..style = PaintingStyle.fill;
    paint0Fill.color = Colors.white.withOpacity(1.0);
    canvas.drawPath(path_0, paint0Fill);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
