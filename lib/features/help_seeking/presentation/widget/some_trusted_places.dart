import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/features/help_seeking/presentation/widget/urgent_help.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../core/utils/url_launcher.dart';
import '../../../../locator.dart';
import '../../data/models/help_seeking_response.dart';

class SomeTrustedPlaces extends StatelessWidget {
  final List<TrustedPlace>? trustedplaces;

  const SomeTrustedPlaces({super.key, this.trustedplaces});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      color: AppColors.lightRed,
      padding: const EdgeInsets.only(),
      child: Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
          color: AppColors.lightBlue,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Gap(24),
              Text('Explore some trusted support options in Australia',
                  style: textTheme.sectionHeading),
              const Gap(8),
              Text(
                "Gotcha4Life itself does not provide crisis intervention or counselling so we've created this list of services that can help.",
                style: isIos
                    ? textTheme.bodyRegular.copyWith(fontSize: 15)
                    : textTheme.bodyRegular,
              ),
              const Gap(24),
              ...?trustedplaces?.map(
                (place) => UrgentHelpItem(
                  title: place.title ?? 'N/A',
                  subtitle: place.subtitle ?? 'N/A',
                  onTap: () {
                    UrlLauncher.launchURL(
                      place.linkOrContactNo ?? '',
                      context: context,
                    );
                    // mixpanel
                    sl<MixpanelService>()
                        .trackButtonClick('Trusted Places Tapped', properties: {
                      'Page': 'Help Seeking Pathway Page',
                      'Code':
                          'click.help_seeking_pathway_page.trusted_places_tapped',
                      'Detail': place.title ?? 'N/A',
                    });
                  },
                  imageUrl: place.iconLink ??
                      'https://images.squarespace-cdn.com/content/v1/600a06cbd2a8133c3598b77e/0091dd0c-3f06-4d54-99f6-d48327e771d1/logo.png?format=1500w', // Using provided URL
                  link: place.linkOrContactNo ?? '',
                ),
              ),
              const Gap(32)
            ],
          ),
        ),
      ),
    );
  }
}
